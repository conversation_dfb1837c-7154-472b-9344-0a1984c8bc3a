{"buildFiles": ["C:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\mobile app\\flutter_application_1\\android\\app\\.cxx\\Debug\\4i4r4v4l\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\mobile app\\flutter_application_1\\android\\app\\.cxx\\Debug\\4i4r4v4l\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}